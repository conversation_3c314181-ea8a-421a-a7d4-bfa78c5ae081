from collections import defaultdict
import pickle

class DetectTrieNode:
    """DetectTrie节点优化结构"""
    __slots__ = ['children', 'source_as_set']  # 内存优化
    
    def __init__(self):
        self.children = {'0': None, '1': None}
        self.source_as_set = set()  # 存储合法AS集合

class DetectTrie:
    """高效前缀检测树"""
    def __init__(self):
        self.root = DetectTrieNode()
        self.conflict_log = defaultdict(set)  # 记录冲突事件

    def insert(self, binary_prefix: str, source_as: int):
        """插入二进制格式前缀（带冲突检测）"""
        node = self.root
        for bit in binary_prefix:
            if not node.children[bit]:
                node.children[bit] = DetectTrieNode()
            node = node.children[bit]

        if node.source_as_set:
            if source_as not in node.source_as_set:
                self.log_conflict(binary_prefix, node.source_as_set, source_as)
        else:
            node.source_as_set.add(source_as)

    def log_conflict(self, prefix, existing_as, new_as):
        """记录前缀冲突事件"""
        self.conflict_log[prefix].update(existing_as)
        self.conflict_log[prefix].add(new_as)

    def batch_insert(self, po_pairs):
        """批量插入P/O对"""
        for prefix, asn in po_pairs:
            self.insert(prefix, asn)
    
    def _search_exact(self, binary_prefix: str) -> set:
        """Private method for exact prefix match search."""
        node = self.root
        for bit in binary_prefix:
            if not node.children.get(bit):
                return set()
            node = node.children[bit]
        return node.source_as_set

    def search_with_fallback(self, binary_prefix: str) -> tuple:
        """Searches for a binary prefix with fallback to parent prefixes.

        Returns:
            tuple: (as_set, matched_prefix_length, is_exact_match)
        """
        # First, try for an exact match.
        as_set = self._search_exact(binary_prefix)
        if as_set:
            return as_set, len(binary_prefix), True

        # If exact match fails, start fallback search.
        for i in range(len(binary_prefix) - 1, 0, -1):
            parent_prefix = binary_prefix[:i]
            as_set = self._search_exact(parent_prefix)
            if as_set:
                return as_set, i, False

        return set(), 0, False

    def save(self, filepath):
        with open(filepath, 'wb') as f:
            pickle.dump(self.root, f)

    @classmethod
    def load(cls, filepath):
        trie = cls()
        with open(filepath, 'rb') as f:
            trie.root = pickle.load(f)
        return trie
def ip_prefix_to_binary(prefix):
    ip_part, length = prefix.split('/')
    length = int(length)
    parts = list(map(int, ip_part.split('.')))
    binary_str = ''.join([format(part, '08b') for part in parts])
    return binary_str[:length]
    
