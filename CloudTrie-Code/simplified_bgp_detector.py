import os
import sys
import pandas as pd
import radix
import pickle
import re
import ipaddress
import subprocess
import json
import numpy as np
from io import StringIO
from datetime import datetime
from multiprocessing import Pool, cpu_count
from dateutil import parser

# Assuming the script is run from the CloudTrie-Code directory
# Add the current directory to the path to ensure modules can be found
sys.path.append(os.getcwd())

from IPTrie import IPTrie
from Detect_Trie import DetectTrie

def normalize_asn(asn):
    """Normalize ASN by removing the 'AS' prefix"""
    return asn.lstrip('AS')

def extract_date_generic(filename):
    """Extract possible date part from filename"""
    match = re.search(r'(\d{4}[-.]?\d{2}[-.]?\d{2})', filename)
    if match:
        try:
            return parser.parse(match.group(1)).strftime('%Y-%m-%d')
        except ValueError:
            return None
    return None

def load_ribs_to_df(fpath):
    """Load RIB data from file into a pandas DataFrame"""
    try:
        # Use bgpdump to process the RIB file and parse it into a DataFrame
        res = subprocess.check_output(["/bgpd/bgpdump", "-q", "-m", "-u", str(fpath)]).decode()
        fmt = "type|timestamp|A/W|peer-ip|peer-asn|prefix|as-path|origin-protocol|next-hop|local-pref|MED|community|atomic-agg|aggregator|unknown-field-1|unknown-field-2"
        cols = fmt.split("|")

        # Select "prefix", "as-path", and "peer-asn" columns
        cols_needed = ["prefix", "as-path","peer-asn"]

        df = pd.read_csv(StringIO(res), sep="|", names=cols, usecols=cols_needed, dtype=str, keep_default_na=False)
        
        return df
    except Exception as e:
        print(f"Error processing file {fpath}: {e}")
        return None

def build_iptrie_from_ribs(rib_files):
    """Build Trie from a list of RIB files"""
    trie = IPTrie()
    total_prefixes = 0
    for file_path in rib_files:
        print(f"Processing RIB file: {file_path}")
        df = load_ribs_to_df(file_path)

        if df is None or df.empty:
            print(f"Skipping file {file_path} due to error or empty DataFrame.")
            continue

        date_from_file = extract_date_generic(os.path.basename(file_path))

        for _, row in df.iterrows():
            prefix = row['prefix']
            as_path = row['as-path']
            peer_as = row['peer-asn']
            source_as = None

            if as_path and as_path != '-':
                as_path_list = as_path.split()
                source_as = as_path_list[-1] if as_path_list else None

            if source_as:
                try:
                    source_as = normalize_asn(source_as)
                    ip_network = ipaddress.ip_network(prefix, strict=False)
                    if isinstance(ip_network.network_address, ipaddress.IPv4Address):
                        prefix_length = ip_network.prefixlen
                        ip_prefix = ''.join(format(int(octet), '08b') for octet in ip_network.network_address.packed)
                        ip_prefix = ip_prefix[:prefix_length]

                        announced = True
                        trie.insert(ip_prefix, source_as, 'RIB', announced, date_from_file, peer_as)
                        total_prefixes += 1

                except ValueError:
                    pass

    print(f"Processed {len(rib_files)} RIB files, total prefixes inserted: {total_prefixes}")
    return trie

# Constants from build-DetectTrie.py
CONFIDENCE_THRESHOLD = 0.1 
SOURCE_WEIGHTS = {
    'RPKI': 0.9,
    'IRR': 0.7,
    'RIB': 0.6
}

def calculate_confidence(asn, sources, peer_count):
    """Heuristic confidence calculation based on original logic."""
    # Since we only use RIB, we simplify the logic.
    # Time persistence score
    time_weights = np.array([np.exp(-i/2) for i in range(5)])
    rib_vector = sources.get('RIB', [0]*5)
    time_score = np.dot(rib_vector, SOURCE_WEIGHTS['RIB'])
    
    # Space consistency score
    space_score = np.sqrt(peer_count) * 0.3
    
    # Membership score (only RIB is present)
    member_score = SOURCE_WEIGHTS['RIB'] * 0.2

    # RPKI/IRR check is skipped as we only have RIB data.
    return 0.5 * time_score + 0.3 * space_score + member_score

def binary_to_ip(binary_str):
    """Converts a binary prefix string to CIDR format."""
    if not isinstance(binary_str, str):
        raise TypeError(f"Expected string, got {type(binary_str)}")
    
    cidr_len = len(binary_str)
    padded = binary_str.ljust(32, '0')
    octets = [str(int(padded[i:i+8], 2)) for i in range(0, 32, 8)]
    return f"{'.'.join(octets)}/{cidr_len}"

def build_detecttrie_from_iptrie(iptrie):
    """Builds a DetectTrie from a given IPTrie."""
    print("Building DetectTrie from IPTrie...")
    detect_trie = DetectTrie()
    po_pairs_to_insert = []

    # Traverse the IPTrie to find all prefix-origin pairs
    node_stack = [(iptrie.root, "")]
    while node_stack:
        node, prefix = node_stack.pop()
        
        if node.is_end_of_prefix:
            for asn, sources in node.source_as.items():
                peer_count = len(sources.get('peers', {}))
                confidence = calculate_confidence(asn, sources, peer_count)
                
                if confidence >= CONFIDENCE_THRESHOLD:
                    po_pairs_to_insert.append((prefix, asn))

        for bit, child_node in node.children.items():
            if child_node:
                node_stack.append((child_node, prefix + bit))
    
    # Batch insert high-confidence pairs into the DetectTrie
    print(f"Found {len(po_pairs_to_insert)} high-confidence P/O pairs to insert.")
    for prefix, asn in po_pairs_to_insert:
        detect_trie.insert(prefix, asn)
        
    return detect_trie

def ip_to_binary(prefix):
    """Converts a CIDR prefix string to a binary string."""
    try:
        ip_network = ipaddress.ip_network(prefix, strict=False)
        if isinstance(ip_network, ipaddress.IPv4Network):
            ip_bin = ''.join(f"{int(octet):08b}" for octet in ip_network.network_address.packed)
            return ip_bin[:ip_network.prefixlen]
    except ValueError:
        return None
    return None

def load_updates_from_txt(fpath):
    """Loads BGP updates from a simple text file (prefix|origin_as)."""
    updates = []
    with open(fpath, 'r') as f:
        for line in f:
            parts = line.strip().split('|')
            if len(parts) == 2:
                updates.append({'prefix': parts[0], 'origin_as': parts[1]})
    return pd.DataFrame(updates)

def detect_hijacks(detect_trie, updates_file, event_name):
    """Detects hijacks by checking updates against the DetectTrie."""
    print(f"Detecting hijacks from {updates_file}...")
    updates_df = load_updates_from_txt(updates_file)
    hijack_events = []

    for _, update in updates_df.iterrows():
        prefix = update['prefix']
        try:
            suspected_as = normalize_asn(update['origin_as'])
        except:
            continue # Skip if ASN is invalid

        binary_prefix = ip_to_binary(prefix)
        if not binary_prefix:
            continue # Skip if prefix is invalid

        # Search the DetectTrie with fallback
        legitimate_asns, matched_len, is_exact = detect_trie.search_with_fallback(binary_prefix)

        if not legitimate_asns or (legitimate_asns and suspected_as not in legitimate_asns):
            event = {
                'timestamp': datetime.utcnow().isoformat(),
                'prefix': prefix,
                'suspected_as': suspected_as,
                'legitimate_asns': list(legitimate_asns)
            }
            hijack_events.append(event)
            hijack_type = "TYPE 1 (Invalid Origin AS)" if legitimate_asns else "TYPE 0 (Unknown Prefix)"
            print(f"HIJACK DETECTED ({hijack_type}): Prefix {prefix} announced by AS{suspected_as}, expected {legitimate_asns or 'N/A'}")
            event['hijack_type'] = hijack_type
            event['matched_prefix_length'] = matched_len
            event['is_exact_match'] = is_exact

    # Save results to a file
    output_file = f'hijack_report_{event_name}.json'
    with open(output_file, 'w') as f:
        json.dump(hijack_events, f, indent=2)
    
    print(f"Hijack detection finished. Found {len(hijack_events)} potential hijacks. Report saved to {output_file}")
    return hijack_events

def main():
    print("Starting simplified BGP hijack detection workflow for specified events...")
    
    event_directories = [
        "/data/bgp_analysis/hijack-20151106-India_BHARTI_Airtel_hijack",
        "/data/bgp_analysis/hijack-20151204-BackConnect_hijacked_VolumeDrive"
    ]

    for event_dir in event_directories:
        event_name = os.path.basename(event_dir)
        print(f"\n{'='*20} Processing Event: {event_name} {'='*20}")

        rib_directory = event_dir
        updates_file = os.path.join(event_dir, "raw_abnormal_routes.txt")

        if not os.path.exists(updates_file):
            print(f"ERROR: Updates file not found at {updates_file}. Skipping event.")
            continue

        # 1. Build IPTrie from RIB files
        print(f"Step 1: Building IPTrie for {event_name}")
        rib_files = [os.path.join(rib_directory, f) for f in os.listdir(rib_directory) if f.endswith('.gz') or f.endswith('.bz2')]
        if not rib_files:
            print(f"ERROR: No RIB files found in {rib_directory}. Skipping event.")
            continue
        iptrie = build_iptrie_from_ribs(rib_files)

        # 2. Build DetectTrie from IPTrie
        print(f"\nStep 2: Building DetectTrie for {event_name}")
        detect_trie = build_detecttrie_from_iptrie(iptrie)

        # 3. Detect hijacks from updates
        print(f"\nStep 3: Detecting hijacks for {event_name}")
        detect_hijacks(detect_trie, updates_file, event_name)

    print(f"\n{'='*20} All events processed. {'='*20}")

if __name__ == "__main__":
    main()
