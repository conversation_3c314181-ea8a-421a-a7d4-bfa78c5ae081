import datetime
import pickle
from collections import defaultdict

def default_list():
    """Default factory function for defaultdict to create a list of size 5."""
    return [0] * 5

def default_source_dict():
    """Default factory for source dictionaries."""
    return defaultdict(default_list)

 
def default_peer_dict():
    """Default factory for peer dictionaries."""
    return defaultdict(default_list)
 
class TrieNode:
    def __init__(self):
        self.children = {'0': None, '1': None}
        self.is_end_of_prefix = False
        self.sources = defaultdict(default_source_dict)
        self.peers = defaultdict(default_peer_dict)

    def get_day_from_date(self, date):
        """计算日期距离2021-01-01的天数差"""
        if date is None:
            return 0
        start_date = datetime.date(2024, 7, 1)
        return (date - start_date).days

    def update_source(self, source_as, source, announced, date, peer_as):
        """更新数据源和peer信息"""
        day = self.get_day_from_date(date) % 5  # 5天周期
        if 0 <= day < 5:
            self.sources[source_as][source][day] = 1 if announced else 0
            if peer_as:
                self.peers[source_as][peer_as][day] = 1

class IPTrie:
    def __init__(self, as_relationships=None):
        self.root = TrieNode()
        self.as_relationships = as_relationships or {}

    def insert(self, ip_prefix, source_as, source, announced, date, peer_as):
        node = self.root
        for bit in ip_prefix:
            if node.children[bit] is None:
                node.children[bit] = TrieNode()
            node = node.children[bit]
        node.is_end_of_prefix = True
        node.update_source(source_as, source, announced, date, peer_as)


    def search(self, ip):
        node = self.root
        for bit in ip:
            if node.children.get(bit) is None:
                return None  # 如果没有找到匹配的路径，返回 None
            node = node.children[bit]
        if node.is_end_of_prefix:
            return node.sources,node.peers
        return None
    def save_to_file(self, file_path):
        """将Trie保存到文件中"""
        with open(file_path, 'wb') as f:
            pickle.dump(self, f)

    @staticmethod
    def load_from_file(file_path):
        """从文件中加载Trie"""
        with open(file_path, 'rb') as f:
            return pickle.load(f)

    def print_trie(self, node=None, path='', level=0, file=None):
        """打印Trie的结构"""
        if node is None:
            node = self.root
        
        indent = ' ' * (level * 2)  # 缩进控制
        if node.is_end_of_prefix:
            line = f"Path: {path}, Sources: {dict(node.sources)},peer-as:{dict(node.peers)}\n"
            # If a file is provided, write to the file, otherwise print to stdout
            if file:
                file.write(line)
            else:
                print(line)
        
        for bit, next_node in node.children.items():
            if next_node is not None:
                self.print_trie(next_node, path + bit, level + 1, file) 

    def print_trie_to_file(self, output_file_path):
        with open(output_file_path, 'w') as file:
            self.print_trie(file=file)

def ip_prefix_to_binary(prefix):
    ip_part, length = prefix.split('/')
    length = int(length)
    parts = list(map(int, ip_part.split('.')))
    binary_str = ''.join([format(part, '08b') for part in parts])
    return binary_str[:length]

