import glob
import pickle
import numpy as np
from collections import defaultdict
from IPTrie import IPTrie,TrieNode,default_list,default_peer_dict,default_source_dict

# 常量定义
THRESHOLD = 0.1 
ANYCAST_PREFIXES = {'0.0.0.0/24', '*******/24', '*******/24'}
WINDOW_SIZE = 5    # 时间窗口长度
SOURCE_WEIGHTS = {
    'RPKI': 0.9,
    'IRR': 0.7,
    'RIB': 0.6
}
class DetectTrieNode:
    """DetectTrie节点优化结构"""
    __slots__ = ['children', 'source_as_set']  # 内存优化
    
    def __init__(self):
        self.children = {'0': None, '1': None}
        self.source_as_set = set()  # 存储合法AS集合

class DetectTrie:
    """高效前缀检测树"""
    def __init__(self):
        self.root = DetectTrieNode()
        self.conflict_log = defaultdict(set)  # 记录冲突事件

    def insert(self, binary_prefix: str, source_as: int):
        """插入二进制格式前缀（带冲突检测）"""
        node = self.root
        for bit in binary_prefix:
            if not node.children[bit]:
                node.children[bit] = DetectTrieNode()
            node = node.children[bit]
        
        # 冲突检测逻辑（论文4.4节）
        if source_as not in node.source_as_set:
            node.source_as_set.add(source_as)

    def log_conflict(self, prefix, existing_as, new_as):
        """记录前缀冲突事件"""
        self.conflict_log[prefix].update(existing_as)
        self.conflict_log[prefix].add(new_as)

    def batch_insert(self, po_pairs):
        """批量插入P/O对"""
        for prefix, asn in po_pairs:
            self.insert(prefix, asn)

    def save(self, filepath):
        with open(filepath, 'wb') as f:
            pickle.dump(self.root, f)

    @classmethod
    def load(cls, filepath):
        trie = cls()
        with open(filepath, 'rb') as f:
            trie.root = pickle.load(f)
        return trie

def calculate_confidence(asn, sources, peer_count):
    """修正后的置信度计算"""
    # 时间持续性（分数据源计算）
    time_weights = np.array([np.exp(-i/2) for i in range(5)])
    rib_vector = sources.get('RIB', [0]*5)  
    time_score = np.dot(rib_vector, time_weights) * SOURCE_WEIGHTS['RIB']
    
    # 空间一致性（观测点平方根平滑）
    space_score = np.sqrt(peer_count) * 0.3
    
    # 隶属度（最高权重）
    member_score = max(SOURCE_WEIGHTS[src] for src in sources.keys()) * 0.2
    if 'RPKI' in sources or 'IRR' in sources:
        return 0.9
    else:
        return 0.5*time_score + 0.3*space_score + member_score

def binary_to_ip(binary_str):
    """安全处理二进制前缀输入"""
    if not isinstance(binary_str, str):
        raise TypeError(f"Expected string, got {type(binary_str)}")
    
    cidr_len = len(binary_str)
    padded = binary_str.ljust(32, '0')
    octets = [str(int(padded[i:i+8], 2)) for i in range(0, 32, 8)]
    return f"{'.'.join(octets)}/{cidr_len}"
def process_single_trie(trie_path):
    """处理单个IPTrie文件"""
    ip_trie = IPTrie.load_from_file(trie_path)
    po_pairs = []
    
    # 遍历Trie收集所有P/O对
    node_stack = [(ip_trie.root, "")]
    while node_stack:
        node, prefix = node_stack.pop()
        
        if node.is_end_of_prefix:
            ip = binary_to_ip(prefix)
            if ip in ANYCAST_PREFIXES:
                continue
                
            for asn, sources in node.sources.items():
                peer_count = len(node.peers.get(asn, []))
                
                # 计算置信度
                confidence = calculate_confidence(asn, sources, peer_count)

                
                if confidence >= THRESHOLD:
                    po_pairs.append((prefix, asn))
        
        # 继续遍历子节点
        for bit in ['1', '0']:  # 优先处理右子树
            child = node.children[bit]
            if child:
                node_stack.append((child, prefix+bit))
    
    return po_pairs

def build_detect_trie(trie_dir):
    """构建全局检测树"""
    detect_trie = DetectTrie()
    trie_files = glob.glob(f"{trie_dir}/trie_*.dat")
    
    for filepath in trie_files:
        print(f"Processing {filepath}...")
        po_pairs = process_single_trie(filepath)
        detect_trie.batch_insert(po_pairs)
    
    return detect_trie

# 示例使用
if __name__ == "__main__":
    detect_trie = build_detect_trie("/rrc0024-12/trie")
    detect_trie.save("/DetectTrie/detect.dat")
    
