import datetime
import pickle
import re
import os
import subprocess
import ipaddress
import chardet
from io import StringIO
from pathlib import Path
from collections import defaultdict
import pandas as pd
from dateutil import parser
import os
import multiprocessing
from IPTrie import IPTrie, TrieNode, default_list, default_peer_dict, default_source_dict


def count_prefixes(node):
    """递归统计 Trie 中的前缀数量"""
    if node is None:
        return 0

    count = 1 if node.is_end_of_prefix else 0
    for child in node.children.values():
        if child is not None:
            count += count_prefixes(child)
    
    return count

def load_ribs_to_df(fpath):
    """Load RIB data from file into a pandas DataFrame"""
    try:
        # 使用 bgpdump 处理 RIB 文件，并将其解析为 DataFrame
        res = subprocess.check_output(["/bgpd/bgpdump", "-q", "-m", "-u", str(fpath)]).decode()
        fmt = "type|timestamp|A/W|peer-ip|peer-asn|prefix|as-path|origin-protocol|next-hop|local-pref|MED|community|atomic-agg|aggregator|unknown-field-1|unknown-field-2"
        cols = fmt.split("|")

        # 选择 "prefix" 和 "as-path" 两列
        cols_needed = ["prefix", "as-path","peer-asn"]

        # 使用 pd.read_csv 从 RIB 数据中读取指定列
        df = pd.read_csv(StringIO(res), sep="|", names=cols, usecols=cols_needed, dtype=str, keep_default_na=False)
        
        return df
    except Exception as e:
        print(f"Error processing file {fpath}: {e}")
        return None

def build_trie_from_ribs(file_group, trie):
    """Build Trie from RIB files"""
    total_prefixes = 0
    for files in file_group:
        for file_path in files:
            print(f"Processing RIB file: {file_path}")
            df = load_ribs_to_df(file_path)

            if df is None or df.empty:
                print(f"Skipping file {file_path} due to error or empty DataFrame.")
                continue

            date_from_file = extract_date_generic(file_path)

            for _, row in df.iterrows():
                prefix = row['prefix']
                as_path = row['as-path']
                peer_as = row['peer-asn']
                source_as = None

                if as_path and as_path != '-':
                    as_path_list = as_path.split()
                    source_as = as_path_list[-1] if as_path_list else None

                if source_as:
                    try:
                        source_as = normalize_asn(source_as)
                        ip_network = ipaddress.ip_network(prefix, strict=False)
                        if isinstance(ip_network.network_address, ipaddress.IPv4Address):
                            prefix_length = ip_network.prefixlen
                            ip_prefix = ''.join(format(int(octet), '08b') for octet in ip_network.network_address.packed)
                            ip_prefix = ip_prefix[:prefix_length]

                            announced = True
                            trie.insert(ip_prefix, source_as, 'RIB', announced, date_from_file, peer_as)  # 传入 peer_as
                            total_prefixes += 1

                    except ValueError:
                        pass

    print(f"Processed {len(file_group)} RIB files, total prefixes: {total_prefixes}")
    return trie

def build_trie_from_roas(file_group, trie):
    """Build Trie from RPKI (ROA) files"""
    prefix_count = 0
    for files in file_group:
        for file_path in files:
            print(f"Processing ROA file: {file_path}")

            if isinstance(file_path, str):
                current_date = (os.path.basename(file_path))
                '''cuoue'''
            else:
                print(f"Skipping invalid file path: {file_path}")
                continue

            if current_date is None:
                print(f"Warning: Unable to extract date from file: {file_path}")
                continue
            
            print(f"Processing file: {file_path} with date: {current_date}")
            roa_data = pd.read_csv(file_path)

            if roa_data is not None:
                for _, row in roa_data.iterrows():
                    prefix = row['IP Prefix']
                    source_as = row['ASN']

                    if source_as:
                        source_as = normalize_asn(source_as)

                        try:
                            ip_network = ipaddress.ip_network(prefix, strict=False)
                            if isinstance(ip_network.network_address, ipaddress.IPv4Address):
                                prefix_length = ip_network.prefixlen
                                ip_prefix = ''.join(format(int(octet), '08b') for octet in ip_network.network_address.packed)
                                ip_prefix = ip_prefix[:prefix_length]
                                announced = True  # Assume it is announced
                                trie.insert(ip_prefix, source_as, 'RPKI', announced,None,None)
                                prefix_count += 1
                        except ValueError:
                            pass
    print(f"Processed {len(file_group)} ROA files, total prefixes: {prefix_count}")
    return trie

def detect_file_encoding(file_path):
    with open(file_path, 'rb') as file:
        raw_data = file.read(1024)  # 读取前 1KB 数据检测编码
    result = chardet.detect(raw_data)
    return result['encoding']

def build_trie_from_irr(folder_path, trie):
    """Parse IRR data to extract P/O (Prefix-Origin AS) pairs and insert into Trie."""
    po_pairs = set()
    route_pattern = re.compile(r'^(route6?):\s*(\S+)')  # 匹配 route/route6 行
    origin_pattern = re.compile(r'origin:\s*(AS\d+)', re.IGNORECASE)  # 匹配 origin 行
 
    # 遍历 IRR 文件夹中的所有文件
    for file_name in os.listdir(folder_path):
        file_path = os.path.join(folder_path, file_name)
        if not os.path.isfile(file_path):
            continue
 
        encoding = detect_file_encoding(file_path)
        print(f"Processing IRR file: {file_path}")
        with open(file_path, 'r', encoding=encoding, errors='ignore') as file:
            current_prefix = None
            for line in file:
                line = line.strip()
                # 检测 route/route6 行，提取前缀
                route_match = route_pattern.match(line)
                if route_match:
                    current_prefix = route_match.group(2)  # 获取 CIDR 格式的前缀
                    continue
 
                # 检测 origin 行，提取 ASN
                origin_match = origin_pattern.match(line)
                if origin_match and current_prefix:
                    asn = origin_match.group(1).upper()  # 标准化为 AS12345 格式
                    po_pairs.add((current_prefix, asn))
                    current_prefix = None  # 重置，避免跨对象误关联
 
    # 将 P/O 对插入 Trie
    for prefix, asn in po_pairs:
        try:
            ip_network = ipaddress.ip_network(prefix, strict=False)
            if isinstance(ip_network, (ipaddress.IPv4Network, ipaddress.IPv6Network)):
                prefix_length = ip_network.prefixlen
                # 转换 IP 前缀为二进制字符串（仅示例，根据实际 Trie 实现调整）
                if ip_network.version == 4:
                    ip_bin = ''.join(f"{int(octet):08b}" for octet in ip_network.network_address.packed)
                else:
                    ip_bin = ''.join(f"{int(octet):08b}" for octet in ip_network.network_address.packed[:16])  # 简化处理
                ip_bin = ip_bin[:prefix_length]
                # 插入 Trie
                asn = normalize_asn(asn)
                trie.insert(ip_bin, asn, 'IRR', True, None,None)
        except ValueError:
            print(f"Invalid prefix: {prefix} (ASN: {asn})")
 
    print(f"Processed {len(po_pairs)} P/O pairs from IRR data")
    return trie

def process_single_group(group_id, rib_group, roa_group, irr_folder, output_dir):
    """并行处理单个时间组的函数"""
    print(f"Processing group {group_id} in process {multiprocessing.current_process().name}")
   # 构造目标文件路径
    trie_file = os.path.join(output_dir, f"trie_group_{group_id}.dat")
    
    if os.path.exists(trie_file):
        print(f"Trie file {trie_file} already exists, skipping group {group_id}")
        return
    # 初始化Trie树
    trie = IPTrie()
    
    # 构建各数据源的Trie
    trie = build_trie_from_roas(roa_group, trie)
    trie = build_trie_from_ribs(rib_group, trie)
    trie = build_trie_from_irr(irr_folder, trie)
    
    # 保存结果
    trie_file = os.path.join(output_dir, f"trie_group_{group_id}.dat")
    trie.save_to_file(trie_file)
    print(f"Saved Trie for group {group_id} to {trie_file}")
 
def process_folder(rib_folder, roa_folder, irr_folder, output_dir, workers=4):
    """并行处理文件夹"""
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 获取分组结果
    rib_grouped = group_files_by_date(rib_folder)
    roa_grouped = group_files_by_date(roa_folder)
    
    # 生成任务参数列表
    tasks = []
    for group_id, (rib_group, roa_group) in enumerate(zip(rib_grouped, roa_grouped)):
        tasks.append((
            group_id + 1,  # 组号从1开始
            rib_group,
            roa_group,
            str(irr_folder),  # 确保路径字符串类型
            output_dir
        ))
    
    # 创建进程池并行执行
    with multiprocessing.Pool(processes=workers) as pool:
        pool.starmap(process_single_group, tasks)
 



def group_files_by_date(folder_path):
    """Group files by consecutive 5 days"""
    date_to_files = defaultdict(list)

    # Iterate through files in folder
    for file in os.listdir(folder_path):
        file_date = extract_date_generic(file)
        if file_date:
            date_to_files[file_date].append(os.path.join(folder_path, file))

    # Sort the dates
    sorted_dates = sorted(date_to_files.keys())
    grouped_files = []
    temp_group = []

    for i, current_date in enumerate(sorted_dates):
        if not temp_group:
            temp_group.append(current_date)
        else:
            if (current_date - temp_group[-1]).days == 1:  # Check if dates are consecutive
                temp_group.append(current_date)
            else:
                if len(temp_group) >= 5:
                    grouped_files.append([date_to_files[date] for date in temp_group])
                
                # Start a new group starting from the current date
                temp_group = [current_date]

        # Check if the current group has reached 5 days
        if len(temp_group) == 5:
            grouped_files.append([date_to_files[date] for date in temp_group])
            temp_group = []  # Reset the group after adding

    # Add the last group if it has at least 5 days
    if len(temp_group) >= 5:
        grouped_files.append([date_to_files[date] for date in temp_group])

    return grouped_files
def extract_date_generic(filename):
    """Extract possible date part from filename"""
    date_candidates = re.findall(r'\d{4}[-_]\d{1,2}[-_]\d{1,2}|\d{8}', filename)
    if date_candidates:
        for date_str in date_candidates:
            normalized_date = date_str.replace('_', '-').replace('-', '')
            try:
                return parser.parse(normalized_date).date()
            except ValueError:
                continue
    return None


def normalize_asn(asn):
    """Normalize ASN by removing the 'AS' prefix"""
    return re.sub(r'^AS', '', asn)


if __name__ == "__main__":
    # 配置路径参数
    rib_folder = Path("/rrc00-24")
    roa_folder = Path("/roa-24")
    irr_folder = Path("/IRR")
    output_dir = "/rrc00-24/trie"
    
    # 启动并行处理（使用4个worker进程）
    process_folder(rib_folder, roa_folder, irr_folder, output_dir, workers=4)
    
    # 验证结果
    trie = IPTrie.load_from_file(os.path.join(output_dir, "trie_group_1.dat"))
    prefix_count = count_prefixes(trie.root)
    print(f"Total number of prefixes in first Trie: {prefix_count}")


