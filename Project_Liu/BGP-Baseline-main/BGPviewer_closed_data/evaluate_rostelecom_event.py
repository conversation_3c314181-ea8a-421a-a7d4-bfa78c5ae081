#!/usr/bin/env python3
"""
专门评估hijack-20170426-PJSC_Rostelecom事件
使用0.95固定阈值，并输出检测出的异常样本的异常概率
"""

import torch
import numpy as np
import os
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import confusion_matrix, precision_score, recall_score, f1_score
from compare_method import MTAD_GAT
from mydataset import SlidingWindowDataset
from config import config
from utils.data_utils import load_event_features_and_labels

def evaluate_with_fixed_threshold(probabilities, true_labels, threshold=0.95):
    """使用固定阈值进行评估"""
    predictions = (probabilities >= threshold).astype(int)
    
    try:
        tn, fp, fn, tp = confusion_matrix(true_labels, predictions).ravel()
    except ValueError:
        # 处理只有一个类别的情况
        if len(np.unique(true_labels)) == 1:
            if true_labels[0] == 0:  # 全是正常样本
                tn = len(true_labels) - np.sum(predictions)
                fp = np.sum(predictions)
                fn = 0
                tp = 0
            else:  # 全是异常样本
                tn = 0
                fp = 0
                fn = len(true_labels) - np.sum(predictions)
                tp = np.sum(predictions)
        else:
            return None
    
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'tp': tp, 'fp': fp, 'tn': tn, 'fn': fn,
        'alarms': tp + fp,
        'false_alarms': fp,
        'detected': tp > 0
    }

def find_optimal_threshold(probabilities, true_labels, min_threshold=0.05):
    """寻找最优分类阈值（用于对比）"""
    thresholds = np.arange(min_threshold, 1.0, 0.05)
    best_f1 = -1
    best_threshold = 0.5
    best_metrics = None
    
    for threshold in thresholds:
        metrics = evaluate_with_fixed_threshold(probabilities, true_labels, threshold)
        if metrics and metrics['f1'] > best_f1:
            best_f1 = metrics['f1']
            best_threshold = threshold
            best_metrics = metrics
    
    return best_threshold, best_f1, best_metrics

def evaluate_rostelecom_event():
    """评估hijack-20170426-PJSC_Rostelecom事件，使用0.95固定阈值"""
    
    print("="*80)
    print("hijack-20151204-BackConnect_hijacked_VolumeDrive 事件专项分析")
    print("目标阈值: 0.99")
    print("="*80)
    
    # 设备配置
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 模型路径（使用最佳性能的hijack模型）
    model_path = '/data/Project_Liu/BGP-Baseline-main/BGPviewer_closed_data/trained_models/hijack_checkpoints/hijack_model_epoch=02_val_loss=0.44.ckpt'
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    # 加载模型
    print("📥 加载hijack模型...")
    model = MTAD_GAT.load_from_checkpoint(
        model_path,
        n_features=config.model.n_features,
        window_size=config.model.window_size,
        dropout=config.model.dropout,
        hid_dim=config.model.hid_dim,
        num_classes=config.model.num_classes,
        gru_hid_dim=config.model.gru_hid_dim,
        n_layers=config.model.n_layers,
        training_stage=1
    )
    model.eval()
    model.to(device)
    print("✅ 模型加载成功")
    
    # 事件数据路径
    event_name = "hijack-20151204-BackConnect_hijacked_VolumeDrive"
    base_data_dir = "/data/data/anomaly-event-routedata"
    event_path = os.path.join(base_data_dir, event_name)
    
    print(f"📂 加载事件数据: {event_name}")
    
    # 加载事件数据
    features, labels = load_event_features_and_labels(event_path)
    
    if features is None or labels is None:
        print(f"❌ 无法加载事件数据: {event_path}")
        return
    
    print(f"📊 数据统计:")
    print(f"  - 总时间步数: {len(features)}")
    print(f"  - 特征维度: {features.shape[1]}")
    print(f"  - 异常样本数: {np.sum(labels == 1)}")
    print(f"  - 正常样本数: {np.sum(labels == 0)}")
    print(f"  - 异常比例: {np.sum(labels == 1) / len(labels) * 100:.2f}%")
    
    # 检查是否有异常样本
    anomaly_count = np.sum(labels == 1)
    if anomaly_count == 0:
        print("⚠️ 该事件没有异常样本，跳过评估")
        return
    
    # 标准化特征
    print("🔄 标准化特征...")
    scaler = StandardScaler()
    normalized_features = scaler.fit_transform(features)
    
    # 创建滑动窗口数据集
    window_size = config.model.window_size
    dataset = SlidingWindowDataset(
        data=normalized_features,
        window=window_size,
        y=labels
    )
    
    print(f"🪟 创建滑动窗口数据集 (窗口大小: {window_size})")
    print(f"  - 窗口数量: {len(dataset)}")
    
    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=64,
        shuffle=False,
        num_workers=0
    )
    
    # 模型预测
    print("🔮 进行模型预测...")
    all_probabilities = []
    all_true_labels = []
    
    with torch.no_grad():
        for batch_features, batch_labels in dataloader:
            batch_features = batch_features.to(device)
            
            # 模型输出
            outputs = model(batch_features)
            
            # 获取异常类别的概率
            probabilities = torch.softmax(outputs, dim=1)[:, 1]  # 异常类别概率
            
            all_probabilities.extend(probabilities.cpu().numpy())
            all_true_labels.extend(batch_labels.numpy())
    
    all_probabilities = np.array(all_probabilities)
    all_true_labels = np.array(all_true_labels)
    
    print(f"✅ 预测完成，共 {len(all_probabilities)} 个预测结果")
    
    # 使用固定阈值0.99进行评估
    print("\n" + "="*60)
    print("🎯 使用固定阈值 0.99 进行评估")
    print("="*60)

    target_threshold = 0.99
    target_metrics = evaluate_with_fixed_threshold(all_probabilities, all_true_labels, target_threshold)
    
    if target_metrics:
        print(f"📈 固定阈值 {target_threshold} 评估结果:")
        print(f"  - 总报警数: {target_metrics['alarms']}")
        print(f"  - 虚警数: {target_metrics['false_alarms']}")
        print(f"  - 检测到异常: {'是' if target_metrics['detected'] else '否'}")
        print(f"  - F1分数: {target_metrics['f1']:.4f}")
        print(f"  - 召回率: {target_metrics['recall']:.4f}")
        print(f"  - 精确率: {target_metrics['precision']:.4f}")
        print(f"  - TP: {target_metrics['tp']}, FP: {target_metrics['fp']}")
        print(f"  - TN: {target_metrics['tn']}, FN: {target_metrics['fn']}")
    else:
        print("❌ 固定阈值0.99评估失败")
        return
    
    # 分析检测出的异常样本
    print("\n" + "="*60)
    print("🔍 检测出的异常样本详细分析")
    print("="*60)

    # 找出被检测为异常的样本
    detected_anomalies = all_probabilities >= target_threshold
    detected_indices = np.where(detected_anomalies)[0]

    print(f"使用阈值 {target_threshold} 检测出 {len(detected_indices)} 个异常样本:")

    # 输出每个检测出的样本的详细信息
    for i, idx in enumerate(detected_indices):
        prob = all_probabilities[idx]
        true_label = all_true_labels[idx]
        is_true_anomaly = true_label == 1

        print(f"  样本 {i+1:2d}: 索引={idx:4d}, 概率={prob:.6f}, 真实标签={'异常' if is_true_anomaly else '正常'} {'✓' if is_true_anomaly else '✗'}")

    # 统计真实异常样本的概率分布
    true_anomaly_indices = np.where(all_true_labels == 1)[0]
    print(f"\n📊 真实异常样本概率分析 (共{len(true_anomaly_indices)}个):")

    for i, idx in enumerate(true_anomaly_indices):
        prob = all_probabilities[idx]
        detected = prob >= target_threshold
        print(f"  异常 {i+1:2d}: 索引={idx:4d}, 概率={prob:.6f} {'🎯检测到' if detected else '❌漏检'}")

    # 概率排序分析
    print(f"\n📈 所有样本按概率排序 (Top 20):")
    sorted_indices = np.argsort(all_probabilities)[::-1]  # 降序排列

    for i in range(min(20, len(sorted_indices))):
        idx = sorted_indices[i]
        prob = all_probabilities[idx]
        true_label = all_true_labels[idx]
        is_true_anomaly = true_label == 1
        detected = prob >= target_threshold

        status = "🎯检测" if detected else "  未检测"
        label_str = "异常" if is_true_anomaly else "正常"
        accuracy = "✓" if (detected and is_true_anomaly) or (not detected and not is_true_anomaly) else "✗"

        print(f"  {i+1:2d}. 索引={idx:4d}, 概率={prob:.6f}, {label_str}, {status} {accuracy}")

    # 寻找最佳阈值进行对比
    print("\n" + "="*60)
    print("🔍 与最佳阈值对比")
    print("="*60)

    best_threshold, best_f1, best_metrics = find_optimal_threshold(all_probabilities, all_true_labels)

    if best_metrics:
        print(f"📈 最佳阈值 {best_threshold:.3f} 评估结果:")
        print(f"  - 总报警数: {best_metrics['alarms']}")
        print(f"  - 虚警数: {best_metrics['false_alarms']}")
        print(f"  - 检测到异常: {'是' if best_metrics['detected'] else '否'}")
        print(f"  - F1分数: {best_metrics['f1']:.4f}")
        print(f"  - 召回率: {best_metrics['recall']:.4f}")
        print(f"  - 精确率: {best_metrics['precision']:.4f}")
        print(f"  - TP: {best_metrics['tp']}, FP: {best_metrics['fp']}")
        print(f"  - TN: {best_metrics['tn']}, FN: {best_metrics['fn']}")

        # 对比分析
        print(f"\n📊 0.99阈值 vs 最佳阈值({best_threshold:.3f})对比:")
        print(f"{'指标':<15} {'阈值0.99':<12} {'最佳阈值':<12} {'差异':<10}")
        print("-" * 50)

        metrics_to_compare = ['f1', 'precision', 'recall', 'alarms', 'false_alarms']
        for metric in metrics_to_compare:
            val_099 = target_metrics[metric]
            val_best = best_metrics[metric]
            diff = val_099 - val_best

            if metric in ['f1', 'precision', 'recall']:
                print(f"{metric.upper():<15} {val_099:<12.4f} {val_best:<12.4f} {diff:+.4f}")
            else:
                print(f"{metric.upper():<15} {val_099:<12} {val_best:<12} {diff:+}")

    # 概率分布统计
    print(f"\n📈 预测概率分布统计:")
    print(f"  - 最小值: {all_probabilities.min():.6f}")
    print(f"  - 最大值: {all_probabilities.max():.6f}")
    print(f"  - 平均值: {all_probabilities.mean():.6f}")
    print(f"  - 中位数: {np.median(all_probabilities):.6f}")
    print(f"  - 标准差: {all_probabilities.std():.6f}")

    # 分析不同概率区间的样本分布
    print(f"\n概率区间分布:")
    intervals = [(0.0, 0.1), (0.1, 0.3), (0.3, 0.5), (0.5, 0.7), (0.7, 0.9), (0.9, 1.0)]
    for low, high in intervals:
        mask = (all_probabilities >= low) & (all_probabilities < high)
        count = np.sum(mask)
        anomaly_in_interval = np.sum(all_true_labels[mask] == 1) if count > 0 else 0
        print(f"  [{low:.1f}, {high:.1f}): {count:>6} 样本 ({anomaly_in_interval:>4} 异常)")

    print("\n" + "="*80)
    print("✅ 分析完成")
    print("="*80)

    return target_metrics, all_probabilities, all_true_labels

if __name__ == "__main__":
    evaluate_rostelecom_event()
